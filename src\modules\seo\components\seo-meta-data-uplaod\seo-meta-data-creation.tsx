import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import FormSubmission from "@/modules/catalog/components/form-submission";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import useSeoMetaDataUpload from "../../hooks/use-seo-meta-data-upload";
import { useState } from "react";
import LanguageTabs from "@/modules/catalog/components/brands/brand-upload/language-tabs";
import { Language } from "../../types/multilanguage-seo";

export default function SeoMetaDataCreation() {
  const [activeLanguage, setActiveLanguage] = useState("french");
  const t = useTranslations("BrandsManagement");
  const {
    metaTitle,
    metaDescription,
    keywords,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData();
  const { submitMetaData, warning, isPending } = useSeoMetaDataUpload(
    getMultilanguageMetaContent
  );

  const router = useRouter();
  const uploadContent = useTranslations("shared.forms.upload");

  return !(
    keywords === undefined ||
    metaDescription === undefined ||
    metaTitle === undefined
  ) ? (
    <div className="flex flex-col py-3">
      <FormSubmission
        cancel={uploadContent("cancel")}
        submit={uploadContent("save")}
        isPending={isPending}
        onCancel={() => router.push("/landing-page-seo")}
        onSubmit={submitMetaData}
      >
        <div className="space-y-4">
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
            <LanguageTabs
              options={[
                { key: "arabic", value: t("languages.arabic") },
                { key: "french", value: t("languages.french") },
                { key: "english", value: t("languages.english") },
              ]}
              onSelect={(language) => {
                setActiveLanguage(language);
              }}
              selectedValue={activeLanguage}
            />
            <div className="text-red self-center">{warning}</div>
            <MultilanguageSeoContent
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              keywords={keywords}
              activeLanguage={activeLanguage as Language}
              changeMetaTitle={handleMetaTitleChange}
              changeMetaDescription={handleMetaDescriptionChange}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
            />
          </div>
        </div>
      </FormSubmission>
    </div>
  ) : (
    <div className="py-2 w-full flex XL:flex-row flex-col XL:gap-8 gap-4">
      <DashboardListsContainerSkeleton>
        {/* keywords & SEO Settings Section */}
        <div className="w-full flex flex-col space-y-4">
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* keywords */}
            <Skeleton className="h-9 w-full" /> {/* Add keywords input */}
            <div className="flex gap-2 mt-2">
              <Skeleton className="h-8 w-24" /> {/* Keyword chip */}
              <Skeleton className="h-8 w-24" /> {/* Keyword chip */}
              <Skeleton className="h-8 w-32" /> {/* Keyword chip */}
            </div>
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* SEO Settings */}
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-24" /> {/* Meta-Title */}
              <Skeleton className="h-9 w-full" />
            </div>
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-32" /> {/* Meta-Description */}
              <Skeleton className="h-9 w-full" />
            </div>
          </div>
        </div>
      </DashboardListsContainerSkeleton>
    </div>
  );
}
