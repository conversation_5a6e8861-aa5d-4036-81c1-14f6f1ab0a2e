"use client";
import { useState } from "react";
import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import TextEditor from "@/components/text-editor";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import useEditableBlog from "../hooks/use-editable-blog";
import useMultilanguageBlogEdition from "../hooks/use-multilanguage-blog-edition";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import FormSubmission from "@/modules/catalog/components/form-submission";
import ImageUpload from "@/modules/page-palette/components/image-upload";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import LanguageTabs from "@/modules/catalog/components/brands/brand-upload/language-tabs";

interface Props {
  blogSlug?: string;
}

export default function BlogEdition({ blogSlug }: Props) {
  const t = useTranslations("BlogsManagement");
  const uploadContent = useTranslations("shared.forms.upload");

  const [activeLanguage, setActiveLanguage] = useState("french");

  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const {
    blog,
    isLoading: blogIsLoading,
    refetch,
  } = useEditableBlog({
    blogSlug: blogSlug || "",
  });

  const {
    metaTitle,
    metaDescription,
    keywords,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData();

  const { categories, categoriesAreLoading } = useCategories();

  const { formRef, submitBlog, warning, isPending } =
    useMultilanguageBlogEdition(
      () => getMultilanguageMetaContent(),
      () => blog?.id || "",
      () => {
        refetch();
      },
      (newSlug: string) => {
        const currentPath = window.location.pathname;
        const newPath = currentPath.replace(
          /\/blogs\/[^\/]+\/edition/,
          `/blogs/${newSlug}/edition`
        );
        router.replace(newPath);
      }
    );

  const cancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/blogs"))
      router.push(previousUrl);
    else router.push("/blogs");
  };

  return !(blogIsLoading || categoriesAreLoading) ? (
    blog && categories && (
      <FormSubmission
        submit={uploadContent("save")}
        cancel={uploadContent("cancel")}
        isPending={isPending}
        onCancel={cancelSubmission}
        onSubmit={submitBlog}
      >
        <div className="p-3 bg-white flex flex-col space-y-6">
          <LanguageTabs
            options={[
              { key: "arabic", value: uploadContent("languages.arabic") },
              { key: "french", value: uploadContent("languages.french") },
              { key: "english", value: uploadContent("languages.english") },
            ]}
            onSelect={(language) => {
              setActiveLanguage(language);
            }}
            selectedValue={activeLanguage}
          />
          <Text textStyle="TS4" className="font-bold text-black">
            {t("blogInfo")}
          </Text>

          <form ref={formRef} className="flex flex-col extraXL:flex-row gap-4">
            <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
                <div className="text-red self-center">{warning}</div>

                {/* Title */}
                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="title">
                    {`${uploadContent("blogLabels.title")} ${uploadContent(
                      "required"
                    )}`}
                  </Label>
                  <WarnInput
                    id="title"
                    name="title"
                    value={blog?.title || ""}
                    warning=""
                  />
                </div>

                {/* Description */}
                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="description">
                    {`${uploadContent(
                      "blogLabels.description"
                    )} ${uploadContent("optional")}`}
                  </Label>
                  <TextEditor
                    name="description"
                    placeholder={uploadContent("blogLabels.description")}
                    initialContent={blog?.description || ""}
                  />
                </div>

                {/* Details */}
                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="details">
                    {`${uploadContent("blogLabels.details")} ${uploadContent(
                      "required"
                    )}`}
                  </Label>
                  <TextEditor
                    name="details"
                    initialContent={blog?.details || ""}
                  />
                </div>

                {/* Display Order */}
                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="displayOrder">
                    {`${uploadContent(
                      "blogLabels.displayOrder"
                    )} ${uploadContent("optional")}`}
                  </Label>
                  <WarnInput
                    id="displayOrder"
                    name="displayOrder"
                    type="number"
                    value={blog?.displayOrder || ""}
                    warning=""
                    placeholder="10"
                  />
                </div>

                {/* Image */}
                <div className="w-full flex flex-col space-y-2">
                  <Label>{uploadContent("blogLabels.image")}</Label>
                  <ImageUpload name="image" defaultSrc={blog?.image || ""} />
                </div>
              </div>
            </div>

            <div className="basis-[30%] order-2 space-y-4">
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
                <MultilanguageSeoContent
                  metaTitle={metaTitle}
                  metaDescription={metaDescription}
                  keywords={keywords}
                  activeLanguage={activeLanguage as any}
                  changeMetaTitle={handleMetaTitleChange}
                  changeMetaDescription={handleMetaDescriptionChange}
                  addNewKeyword={addNewKeyword}
                  removeKeyword={removeKeyword}
                />
              </div>
            </div>
          </form>
        </div>
      </FormSubmission>
    )
  ) : (
    <DashboardListsContainerSkeleton className="flex-1">
      {Array.from({ length: 6 }).map((_, idx) => (
        <div key={idx} className="w-full flex flex-col space-y-1">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-9 w-full max-w-[500px]" />
        </div>
      ))}
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
    </DashboardListsContainerSkeleton>
  );
}
